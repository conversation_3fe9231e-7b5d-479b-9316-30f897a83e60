<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Scroll</title>
    <style>
      p {
        height: 2000px;
        width: 120%;
      }

      button {
        position: fixed;
        bottom: 10px;
        right: 10px;
      }
    </style>
  </head>
  <body>
    <p>
      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod
      tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim
      veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea
      commodo consequat. Duis aute irure dolor in reprehenderit in voluptate
      velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat
      cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id
      est laborum
    </p>
    <button onclick="scrollToTop()">Scroll to Top</button>
    <script>
      function scrollToTop() {
        let documentHeight = document.documentElement.offsetHeight;
        let viewportHeight = window.innerHeight;
        // window.scrollTo(0, documentHeight - viewportHeight);
        window.scrollTo({
          left: 0,
          top: documentHeight - viewportHeight,
          behavior: "smooth",
        });
      }

      //   setInterval(() => {
      //     scrollBy(0, 50);
      //   }, 500);

      console.log("offsetWidth = ", document.body.offsetWidth);
      console.log("offsetHeight = ", document.body.offsetHeight);
      console.log("offsetLeft = ", document.body.offsetLeft);
      console.log("offsetTop = ", document.body.offsetTop);
      console.log("clientWidth = ", document.body.clientWidth);
      console.log("clientHeight = ", document.body.clientHeight);
      console.log("clientLeft = ", document.body.clientLeft);
      console.log("clientTop = ", document.body.clientTop);
      console.log("scrollWidth = ", document.body.scrollWidth);
      console.log("scrollHeight = ", document.body.scrollHeight);
      console.log("scrollLeft = ", document.body.scrollLeft);
      console.log("scrollTop = ", document.body.scrollTop);
    </script>
  </body>
</html>
