<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Custom Elements</title>
  </head>
  <body>
    <p>
      The document has one marble: <inline-circle></inline-circle>. The HTML
      parser instantiates two more marbles:
      <inline-circle diameter="1.2em" color="blue"></inline-circle>
      <inline-circle diameter=".6em" color="gold"></inline-circle>. How many
      marbles does the document contain now?
    </p>
    <script>
      customElements.define(
        "inline-circle",
        class InlineCircle extends HTMLElement {
          connectedCallback() {
            this.style.display = "inline-block";
            this.style.borderRadius = "50%";
            this.style.border = "solid black 1px";
            this.style.transform = "translateY(10%)";
            if (!this.style.width) {
              this.style.width = "0.8em";
              this.style.heigth = "0.8em";
            }
          }

          static get observedAttributes() {
            return ["diameter", "color"];
          }

          attrubuteChangedCallback(name, oldValue, newValue){
            switch(name){
                case "diameter":
                    this.style.width = newValue;
                    this.style.height = newValue;
                    break;
                case "color":
                    this.style.backgroundColor = newValue;
                    break;
            }
          }

          get diameter(){
            return this.getAttribute("diameter");
          }

          set diameter(value){
            this.setAttribute("diameter", value);
          }

          get color(){
            return this.getAttribute("color");
          }

          set color(value){
            this.setAttribute("color", value);
          }
        }
      );
    </script>
  </body>
</html>
