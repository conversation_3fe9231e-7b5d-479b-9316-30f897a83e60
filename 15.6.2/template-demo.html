<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Template 元素演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        tr:hover {
            background-color: #f5f5f5;
        }
        
        .controls {
            margin: 20px 0;
        }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        .code-example {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
        }
        
        .highlight {
            background-color: yellow;
            padding: 2px 4px;
        }
    </style>
</head>
<body>
    <h1>HTML Template 元素演示</h1>
    
    <div class="code-example">
<strong>核心代码解释：</strong>
let tableBody = document.querySelector("tbody");     // 1. 获取表格主体
let template = document.querySelector("#row");       // 2. 获取模板
let clone = template.content.cloneNode(true);       // 3. 深度克隆模板内容
// ...在这里修改克隆内容的 &lt;td&gt; 元素...
tableBody.append(clone);                            // 4. 添加到表格中
    </div>

    <div class="controls">
        <button onclick="addEmployee()">添加员工</button>
        <button onclick="addRandomEmployee()">添加随机员工</button>
        <button onclick="clearTable()">清空表格</button>
    </div>

    <table>
        <thead>
            <tr>
                <th>姓名</th>
                <th>职位</th>
                <th>部门</th>
                <th>薪资</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody id="employeeTable">
            <!-- 动态生成的行将添加到这里 -->
        </tbody>
    </table>

    <!-- Template 定义 - 这部分不会在页面中显示 -->
    <template id="row">
        <tr>
            <td class="name">姓名</td>
            <td class="position">职位</td>
            <td class="department">部门</td>
            <td class="salary">薪资</td>
            <td class="actions">
                <button onclick="editRow(this)" style="background-color: #28a745;">编辑</button>
                <button onclick="deleteRow(this)" style="background-color: #dc3545;">删除</button>
            </td>
        </tr>
    </template>

    <h2>Template 的优势</h2>
    <ul>
        <li><strong>性能优化</strong>：模板内容不会被解析和渲染，直到被克隆使用</li>
        <li><strong>代码复用</strong>：一个模板可以被多次克隆使用</li>
        <li><strong>结构清晰</strong>：HTML 结构在模板中定义，JavaScript 只负责数据填充</li>
        <li><strong>安全性</strong>：避免了字符串拼接可能带来的 XSS 风险</li>
    </ul>

    <script>
        // 示例数据
        const sampleEmployees = [
            { name: "张三", position: "前端开发", department: "技术部", salary: "15000" },
            { name: "李四", position: "后端开发", department: "技术部", salary: "18000" },
            { name: "王五", position: "产品经理", department: "产品部", salary: "20000" },
            { name: "赵六", position: "UI设计师", department: "设计部", salary: "12000" }
        ];

        let employeeCount = 0;

        function addEmployee() {
            // 这就是你问的那段代码的完整实现
            let tableBody = document.querySelector("#employeeTable");
            let template = document.querySelector("#row");
            let clone = template.content.cloneNode(true); // deep clone
            
            // 使用 DOM 向克隆的 <td> 元素插入内容
            const employee = sampleEmployees[employeeCount % sampleEmployees.length];
            
            clone.querySelector(".name").textContent = employee.name;
            clone.querySelector(".position").textContent = employee.position;
            clone.querySelector(".department").textContent = employee.department;
            clone.querySelector(".salary").textContent = employee.salary;
            
            // 现在将克隆并初始化的行添加到表格中
            tableBody.append(clone);
            
            employeeCount++;
            
            console.log("添加了员工:", employee);
        }

        function addRandomEmployee() {
            let tableBody = document.querySelector("#employeeTable");
            let template = document.querySelector("#row");
            let clone = template.content.cloneNode(true);
            
            // 生成随机数据
            const names = ["小明", "小红", "小刚", "小丽", "小华"];
            const positions = ["开发工程师", "测试工程师", "运维工程师", "数据分析师"];
            const departments = ["技术部", "产品部", "运营部", "市场部"];
            
            const randomEmployee = {
                name: names[Math.floor(Math.random() * names.length)],
                position: positions[Math.floor(Math.random() * positions.length)],
                department: departments[Math.floor(Math.random() * departments.length)],
                salary: (Math.floor(Math.random() * 20) + 10) * 1000
            };
            
            clone.querySelector(".name").textContent = randomEmployee.name;
            clone.querySelector(".position").textContent = randomEmployee.position;
            clone.querySelector(".department").textContent = randomEmployee.department;
            clone.querySelector(".salary").textContent = randomEmployee.salary;
            
            tableBody.append(clone);
            
            console.log("添加了随机员工:", randomEmployee);
        }

        function editRow(button) {
            const row = button.closest('tr');
            const name = row.querySelector('.name').textContent;
            alert(`编辑员工: ${name}`);
        }

        function deleteRow(button) {
            const row = button.closest('tr');
            const name = row.querySelector('.name').textContent;
            if (confirm(`确定要删除员工 ${name} 吗？`)) {
                row.remove();
            }
        }

        function clearTable() {
            const tableBody = document.querySelector("#employeeTable");
            tableBody.innerHTML = '';
            employeeCount = 0;
        }

        // 演示 template.content 的特性
        console.log("=== Template 演示 ===");
        console.log("template 元素:", document.querySelector("#row"));
        console.log("template.content:", document.querySelector("#row").content);
        console.log("template 在页面中可见吗？", 
                   getComputedStyle(document.querySelector("#row")).display !== 'none');
    </script>
</body>
</html>
