<!DOCTYPE html>

<html>
  <head>
    <title>Digital Clock</title>
    <style>
      #clock {
        font: bold 24px sans-serif;
        background: #ddf;
        padding: 15px;
        border: solid black 2px;
        border-radius: 10px;
      }
    </style>
  </head>
  <body>
    <h1>Digital Clock</h1>
    <span id="clock"></span>
    <script>
      function displayTime() {
        let clock = document.querySelector("#clock");
        let now = new Date();
        clock.textContent = now.toLocaleTimeString();
      }
      displayTime();
      setInterval(displayTime, 1000);
    </script>
  </body>
</html>
